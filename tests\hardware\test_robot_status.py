# tests/hardware/test_robot_status.py

import sys
import os

# 将项目根目录添加到Python路径中
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../'))
sys.path.insert(0, PROJECT_ROOT)

# 将 lib 目录也明确添加到路径中
LIB_PATH = os.path.join(PROJECT_ROOT, 'lib')
if LIB_PATH not in sys.path:
    sys.path.insert(0, LIB_PATH)

# 导入配置和接口
from config import ROBOT_IP, ROBOT_PORT
from src.hardware.robot_interface import RobotInterface

# 确保能够导入nrc_interface
try:
    import nrc_interface as nrc
    print("成功导入nrc_interface模块")
except ImportError as e:
    print(f"导入nrc_interface失败: {e}")
    print(f"当前Python路径: {sys.path}")
    print(f"尝试从以下位置导入: {LIB_PATH}")
    # 尝试直接从lib目录导入
    sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../lib')))
    try:
        import nrc_interface as nrc
        print("通过绝对路径成功导入nrc_interface模块")
    except ImportError as e:
        print(f"再次尝试导入失败: {e}")
        print("请确保lib目录中包含nrc_interface.py和nrc_host.pyd文件")

class RobotStatusReader:
    """机械臂状态读取器 - 读取所有可用的机器人状态信息"""

    def __init__(self, robot_interface: RobotInterface):
        self.robot = robot_interface
        self.socket_fd = robot_interface.socket_fd



    def read_basic_status(self):
        """读取基本状态信息"""
        print("\n=== 基本状态信息 ===")

        # 连接状态
        conn_status = nrc.get_connection_status(self.socket_fd)
        print(f"连接状态: {conn_status}")

        # 伺服状态
        try:
            servo_status = 0
            result = nrc.get_servo_state(self.socket_fd, servo_status)
            if isinstance(result, list) and len(result) > 1:
                print(f"伺服状态: {result[1]} (返回码: {result[0]})")
            else:
                print(f"伺服状态: {result} (返回码: {result})")
        except Exception as e:
            print(f"伺服状态: 读取失败 - {e}")

        # 机器人运行状态
        try:
            running_status = 0
            result = nrc.get_robot_running_state(self.socket_fd, running_status)
            if isinstance(result, list) and len(result) > 1:
                print(f"运行状态: {result[1]} (返回码: {result[0]})")
            else:
                print(f"运行状态: {result} (返回码: {result})")
        except Exception as e:
            print(f"运行状态: 读取失败 - {e}")

        # 当前速度
        try:
            speed = 0
            result = nrc.get_speed(self.socket_fd, speed)
            if isinstance(result, list) and len(result) > 1:
                print(f"当前速度: {result[1]} (返回码: {result[0]})")
            else:
                print(f"当前速度: {result} (返回码: {result})")
        except Exception as e:
            print(f"当前速度: 读取失败 - {e}")

        # 当前模式
        try:
            mode = 0
            result = nrc.get_current_mode(self.socket_fd, mode)
            if isinstance(result, list) and len(result) > 1:
                print(f"当前模式: {result[1]} (返回码: {result[0]})")
            else:
                print(f"当前模式: {result} (返回码: {result})")
        except Exception as e:
            print(f"当前模式: 读取失败 - {e}")

        # 示教模式类型
        try:
            teach_type = 0
            result = nrc.get_teach_type(self.socket_fd, teach_type)
            if isinstance(result, list) and len(result) > 1:
                print(f"示教模式类型: {result[1]} (返回码: {result[0]})")
            else:
                print(f"示教模式类型: {result} (返回码: {result})")
        except Exception as e:
            print(f"示教模式类型: 读取失败 - {e}")
    
    def read_position_info(self):
        """读取位置信息"""
        print("\n=== 位置信息 ===")

        # 当前坐标系
        try:
            coord = 0
            result = nrc.get_current_coord(self.socket_fd, coord)
            if isinstance(result, list) and len(result) > 1:
                print(f"当前坐标系: {result[1]} (返回码: {result[0]})")
            else:
                print(f"当前坐标系: {result} (返回码: {result})")
        except Exception as e:
            print(f"当前坐标系: 读取失败 - {e}")

        # 当前工具手编号
        try:
            tool_num = 0
            result = nrc.get_tool_hand_number(self.socket_fd, tool_num)
            if isinstance(result, list) and len(result) > 1:
                print(f"当前工具手编号: {result[1]} (返回码: {result[0]})")
            else:
                print(f"当前工具手编号: {result} (返回码: {result})")
        except Exception as e:
            print(f"当前工具手编号: 读取失败 - {e}")

        # 当前用户坐标编号
        try:
            user_num = 0
            result = nrc.get_user_coord_number(self.socket_fd, user_num)
            if isinstance(result, list) and len(result) > 1:
                print(f"当前用户坐标编号: {result[1]} (返回码: {result[0]})")
            else:
                print(f"当前用户坐标编号: {result} (返回码: {result})")
        except Exception as e:
            print(f"当前用户坐标编号: 读取失败 - {e}")

        # 当前位置 (关节坐标系)
        try:
            from nrc_interface import VectorDouble
            joint_pos = VectorDouble()
            ret_code = nrc.get_current_position(self.socket_fd, 0, joint_pos)  # 0表示关节坐标系

            # 将VectorDouble转换为Python列表
            joint_pos_list = []
            for i in range(len(joint_pos)):
                joint_pos_list.append(joint_pos[i])

            print(f"当前关节位置: {joint_pos_list} (返回码: {ret_code})")
        except Exception as e:
            print(f"读取关节位置失败: {e}")

        # 当前位置 (笛卡尔坐标系)
        try:
            cart_pos = VectorDouble()
            ret_code = nrc.get_current_position(self.socket_fd, 1, cart_pos)  # 1表示笛卡尔坐标系

            # 将VectorDouble转换为Python列表
            cart_pos_list = []
            for i in range(len(cart_pos)):
                cart_pos_list.append(cart_pos[i])

            print(f"当前笛卡尔位置: {cart_pos_list} (返回码: {ret_code})")
        except Exception as e:
            print(f"读取笛卡尔位置失败: {e}")

        # 外部轴位置
        try:
            extra_pos = VectorDouble()
            ret_code = nrc.get_current_extra_position(self.socket_fd, extra_pos)

            # 将VectorDouble转换为Python列表
            extra_pos_list = []
            for i in range(len(extra_pos)):
                extra_pos_list.append(extra_pos[i])

            print(f"当前外部轴位置: {extra_pos_list} (返回码: {ret_code})")
        except Exception as e:
            print(f"读取外部轴位置失败: {e}")
    
    def get_robot_type_as_string(self):
        """
        尝试获取机器人类型。
        注意：此API在当前Python绑定中可能不可用，因为它需要特殊的RobotType对象。
        """
        try:
            # 尝试调用API，但预期会失败
            robot_type = 0
            result = nrc.get_robot_type(self.socket_fd, robot_type)

            if isinstance(result, list) and len(result) > 1:
                ret_code, type_code = result[0], result[1]
                if ret_code == 0:
                    # 创建从机器人类型代码到可读字符串的映射字典
                    ROBOT_TYPE_MAP = {
                        0: "无类型 (NOTYPE)",
                        1: "六轴通用机器人 (SIX_AXLE_GENERAL)",
                        2: "四轴SCARA机器人 (FOUR_AXLE_SCARA)",
                        3: "四轴码垛机器人 (FOUR_AXLE_STACK)",
                        4: "四轴通用机器人 (FOUR_AXLE_GENERAL)",
                        5: "单轴通用机器人 (ONE_AXLE_GENERAL)",
                        6: "五轴通用机器人 (FIVE_AXLE_GENERAL)",
                        7: "六轴单臂通用机器人 (SIX_AXLE_ONE_GENERAL)",
                    }
                    type_name = ROBOT_TYPE_MAP.get(type_code, f"未知的类型代码 ({type_code})")
                    return f"{type_name} (代码: {type_code})"
                else:
                    return f"API调用失败，返回码: {ret_code}"
            else:
                return f"API调用异常，结果: {result}"

        except Exception as e:
            # 这是预期的错误，因为API需要特殊的RobotType对象
            if "RobotType &" in str(e):
                return "API不可用 (需要特殊的RobotType对象，在Python绑定中不可用)"
            else:
                return f"调用时发生异常: {e}"

    def read_robot_config(self):
        """读取机器人配置信息"""
        print("\n=== 机器人配置信息 ===")

        # 使用我们新的健壮函数来获取机器人类型
        robot_type_str = self.get_robot_type_as_string()
        print(f"机械臂类型: {robot_type_str}")

        # 机器人配置 - 尝试不同的参数类型
        try:
            # 根据错误信息，API期望一个int参数，可能是配置ID或索引
            # 尝试读取配置ID为0的配置
            config_id = 0
            result = nrc.get_robot_configuration(self.socket_fd, config_id)

            if isinstance(result, list) and len(result) > 1:
                ret_code, config_value = result[0], result[1]
                if ret_code == 0:
                    print(f"机器人配置: {config_value} (返回码: {ret_code})")
                else:
                    print(f"机器人配置: 读取失败，可能需要不同的参数类型 (返回码: {ret_code})")
            else:
                if result == -1:
                    print(f"机器人配置: API调用失败，可能需要VectorInt容器或其他参数类型 (返回码: {result})")
                else:
                    print(f"机器人配置: {result} (返回码: {result})")

        except Exception as e:
            print(f"读取机器人配置时发生异常: {e}")

    def read_motor_status(self):
        """读取电机状态信息"""
        print("\n=== 电机状态信息 ===")

        # 电机扭矩
        try:
            from nrc_interface import VectorInt
            motor_torque = VectorInt()
            motor_torque_sync = VectorInt()
            ret_code = nrc.get_curretn_motor_torque(self.socket_fd, motor_torque, motor_torque_sync)

            # 转换为Python列表
            torque_list = [motor_torque[i] for i in range(len(motor_torque))]
            torque_sync_list = [motor_torque_sync[i] for i in range(len(motor_torque_sync))]

            print(f"电机扭矩: {torque_list} (返回码: {ret_code})")
            print(f"电机扭矩同步: {torque_sync_list}")
        except Exception as e:
            print(f"读取电机扭矩失败: {e}")

        # 电机转速
        try:
            motor_speed = VectorInt()
            motor_speed_sync = VectorInt()
            ret_code = nrc.get_curretn_motor_speed(self.socket_fd, motor_speed, motor_speed_sync)

            # 转换为Python列表
            speed_list = [motor_speed[i] for i in range(len(motor_speed))]
            speed_sync_list = [motor_speed_sync[i] for i in range(len(motor_speed_sync))]

            print(f"电机转速: {speed_list} (返回码: {ret_code})")
            print(f"电机转速同步: {speed_sync_list}")
        except Exception as e:
            print(f"读取电机转速失败: {e}")

        # 电机负载
        try:
            from nrc_interface import VectorDouble
            motor_payload = VectorDouble()
            motor_payload_sync = VectorDouble()
            ret_code = nrc.get_curretn_motor_payload(self.socket_fd, motor_payload, motor_payload_sync)

            # 转换为Python列表
            payload_list = [motor_payload[i] for i in range(len(motor_payload))]
            payload_sync_list = [motor_payload_sync[i] for i in range(len(motor_payload_sync))]

            print(f"电机负载: {payload_list} (返回码: {ret_code})")
            print(f"电机负载同步: {payload_sync_list}")
        except Exception as e:
            print(f"读取电机负载失败: {e}")

        # 末端线速度和轴速度
        try:
            line_speed = 0.0
            joint_speed = VectorDouble()
            joint_speed_sync = VectorDouble()
            ret_code = nrc.get_curretn_line_speed_and_joint_speed(self.socket_fd, line_speed, joint_speed, joint_speed_sync)

            # 转换为Python列表
            joint_speed_list = [joint_speed[i] for i in range(len(joint_speed))]
            joint_speed_sync_list = [joint_speed_sync[i] for i in range(len(joint_speed_sync))]

            print(f"末端线速度: {line_speed} (返回码: {ret_code})")
            print(f"关节速度: {joint_speed_list}")
            print(f"关节速度同步: {joint_speed_sync_list}")
        except Exception as e:
            print(f"读取速度信息失败: {e}")
    
    def read_sensor_data(self):
        """读取传感器数据"""
        print("\n=== 传感器数据 ===")

        # 六维力传感器数据 - 根据错误信息使用VectorInt
        try:
            from nrc_interface import VectorInt
            sensor_data = VectorInt()
            ret_code = nrc.get_sensor_data(self.socket_fd, sensor_data)

            # 转换为Python列表
            sensor_list = [sensor_data[i] for i in range(len(sensor_data))]

            if len(sensor_list) > 0:
                print(f"六维力传感器数据: {sensor_list} (返回码: {ret_code})")
            else:
                print(f"六维力传感器数据: 无数据 (返回码: {ret_code})")
        except Exception as e:
            print(f"读取六维力传感器数据失败: {e}")

    def read_teachbox_status(self):
        """读取示教盒状态"""
        print("\n=== 示教盒状态 ===")

        # 示教盒连接状态 - 使用正确的布尔参数
        try:
            connected = False
            result = nrc.get_teachbox_connection_status(self.socket_fd, connected)

            if isinstance(result, list) and len(result) > 1:
                ret_code, connected_value = result[0], result[1]
            else:
                ret_code, connected_value = result, connected

            if ret_code == 0:
                status_text = "已连接" if connected_value else "未连接"
                print(f"示教盒连接状态: {status_text} (返回码: {ret_code})")
            else:
                print(f"示教盒连接状态: 读取失败 (返回码: {ret_code})")
        except Exception as e:
            print(f"读取示教盒连接状态失败: {e}")

    def read_controller_info(self):
        """读取控制器信息"""
        print("\n=== 控制器信息 ===")

        # 控制器ID - 使用字符串而不是VectorChar
        try:
            controller_id = ""  # 初始化为字符串
            result = nrc.get_controller_id(self.socket_fd, controller_id)
            if isinstance(result, list) and len(result) > 1:
                print(f"控制器ID: {result[1]} (返回码: {result[0]})")
            else:
                print(f"控制器ID: {result} (返回码: {result})")
        except Exception as e:
            print(f"读取控制器ID失败: {e}")

        # 库版本
        try:
            version = nrc.get_library_version()
            print(f"SDK库版本: {version}")
        except Exception as e:
            print(f"读取库版本失败: {e}")

    def read_io_status(self):
        """读取IO状态"""
        print("\n=== IO状态信息 ===")

        # 数字输入状态 - 使用get_digital_input而不是get_force_digital_input
        print("数字输入状态:")
        try:
            from nrc_interface import VectorInt
            digital_inputs = VectorInt()
            ret_code = nrc.get_digital_input(self.socket_fd, digital_inputs)

            # 显示前8个数字输入端口
            for port in range(min(8, len(digital_inputs))):
                print(f"  DI{port}: {digital_inputs[port]} (返回码: {ret_code})")

            if len(digital_inputs) == 0:
                print("  无数字输入数据")
        except Exception as e:
            print(f"  数字输入读取失败: {e}")

        # 模拟输入状态
        print("模拟输入状态:")
        try:
            from nrc_interface import VectorDouble
            ain_data = VectorDouble()
            ret_code = nrc.get_analog_input(self.socket_fd, ain_data)

            # 显示前4个模拟输入端口
            for port in range(min(4, len(ain_data))):
                print(f"  AI{port}: {ain_data[port]} (返回码: {ret_code})")

            if len(ain_data) == 0:
                print("  无模拟输入数据")
        except Exception as e:
            print(f"  模拟输入读取失败: {e}")

    def read_dh_params(self):
        """读取DH参数"""
        print("\n=== DH参数 ===")

        try:
            # 获取DH参数 - 需要传递RobotDHParam对象
            from nrc_interface import RobotDHParam
            dh_param = RobotDHParam()
            ret_code = nrc.get_robot_dh_param(self.socket_fd, dh_param)

            # 显示主要DH参数
            print(f"DH参数 (返回码: {ret_code}):")
            print(f"  L1: {dh_param.L1}")
            print(f"  L2: {dh_param.L2}")
            print(f"  L3: {dh_param.L3}")
            print(f"  L4: {dh_param.L4}")
            print(f"  L5: {dh_param.L5}")
            print(f"  L6: {dh_param.L6}")
        except Exception as e:
            print(f"读取DH参数失败: {e}")

    def read_joint_params(self):
        """读取关节参数"""
        print("\n=== 关节参数 ===")

        # 尝试读取每个关节的参数
        for joint_id in range(6):  # 假设是6轴机器人
            try:
                from nrc_interface import RobotJointParam
                joint_param = RobotJointParam()
                ret_code = nrc.get_robot_joint_param(self.socket_fd, joint_id, joint_param)

                print(f"关节{joint_id}参数 (返回码: {ret_code}):")
                print(f"  减速比: {joint_param.reducRatio}")
                print(f"  编码器分辨率: {joint_param.encoderResolution}")
                print(f"  正软限位: {joint_param.posSWLimit}")
                print(f"  负软限位: {joint_param.negSWLimit}")
                print(f"  额定转速: {joint_param.ratedRotSpeed}")
                print(f"  最大转速: {joint_param.maxRotSpeed}")
            except Exception as e:
                print(f"读取关节{joint_id}参数失败: {e}")

    def read_single_cycle(self):
        """读取单圈值"""
        print("\n=== 单圈值 ===")

        try:
            from nrc_interface import VectorDouble
            single_cycle = VectorDouble()
            ret_code = nrc.get_single_cycle(self.socket_fd, single_cycle)

            # 转换为Python列表
            cycle_list = [single_cycle[i] for i in range(len(single_cycle))]

            print(f"单圈值: {cycle_list} (返回码: {ret_code})")
        except Exception as e:
            print(f"读取单圈值失败: {e}")

    def robot_initialize_and_power_on(self):
        """
        完整的机器人初始化和上电流程
        根据INEXBOT文档实现正确的状态转换逻辑
        """
        import time

        print("开始机器人初始化和上电流程...")

        # 定义状态名称映射
        servo_names = {0: "停止状态", 1: "就绪状态", 2: "报警状态", 3: "运行状态"}

        try:
            # 第1步：无论如何，先清除可能存在的历史错误
            print("步骤 1: 清除错误...")
            result = nrc.clear_error(self.socket_fd)
            if result != 0:
                print(f"警告：清除错误返回码: {result}")
            else:
                print("清除错误成功")

            # 延时一小段时间，等待控制器响应
            time.sleep(0.5)

            # 第2步：获取当前伺服状态，以决定下一步操作
            servo_status = 0
            result = nrc.get_servo_state(self.socket_fd, servo_status)
            if isinstance(result, list) and len(result) > 1:
                ret_code, current_state = result[0], result[1]
            else:
                ret_code, current_state = result, servo_status

            if ret_code != 0:
                print(f"错误：无法获取伺服状态！返回码: {ret_code}")
                return False

            print(f"步骤 2: 获取到当前伺服状态为 {current_state} ({servo_names.get(current_state, '未知状态')})")

            # 第3步：根据当前状态，执行不同的逻辑分支，最终目标是进入状态1（就绪）
            if current_state == 2:  # 报警状态
                print("状态2 (报警): 清错后需要重新检查状态...")
                # 再次尝试清错
                result = nrc.clear_error(self.socket_fd)
                time.sleep(1)
                servo_status = 0
                result = nrc.get_servo_state(self.socket_fd, servo_status)
                if isinstance(result, list) and len(result) > 1:
                    ret_code, current_state = result[0], result[1]
                else:
                    ret_code, current_state = result, servo_status

                if current_state == 2:
                    print("错误：清错后仍处于报警状态，可能存在持续的硬件错误")
                    return False
                print(f"清错后状态变为: {current_state} ({servo_names.get(current_state, '未知状态')})")

            elif current_state == 3:  # 运行状态
                print("状态3 (运行): 检测到机器人已在运行，现在执行下电操作以进入就绪态...")
                result = nrc.set_servo_poweroff(self.socket_fd)
                if result != 0:
                    print(f"错误：从运行状态下电失败！返回码: {result}")
                    return False
                time.sleep(1)  # 等待下电完成
                print("下电完成，机器人应进入状态1。")

                # 验证下电结果
                servo_status = 0
                result = nrc.get_servo_state(self.socket_fd, servo_status)
                if isinstance(result, list) and len(result) > 1:
                    ret_code, current_state = result[0], result[1]
                else:
                    ret_code, current_state = result, servo_status
                print(f"下电后状态: {current_state} ({servo_names.get(current_state, '未知状态')})")

            elif current_state == 0:  # 停止状态
                print("状态0 (停止): 准备设置伺服为就绪状态...")
                result = nrc.set_servo_state(self.socket_fd, 1)  # 设置为就绪状态
                if result != 0:
                    print(f"错误：从停止状态设置伺服就绪失败！返回码: {result}")
                    return False
                time.sleep(0.5)
                print("设置伺服就绪成功，机器人应进入状态1。")

                # 验证设置结果
                servo_status = 0
                result = nrc.get_servo_state(self.socket_fd, servo_status)
                if isinstance(result, list) and len(result) > 1:
                    ret_code, current_state = result[0], result[1]
                else:
                    ret_code, current_state = result, servo_status
                print(f"设置后状态: {current_state} ({servo_names.get(current_state, '未知状态')})")

            elif current_state == 1:  # 已经是就绪状态
                print("状态1 (就绪): 机器人已处于就绪状态，可以直接上电。")

            # 经过以上步骤，机器人状态理论上应该为 1 (就绪)
            # 再次确认一下
            servo_status = 0
            result = nrc.get_servo_state(self.socket_fd, servo_status)
            if isinstance(result, list) and len(result) > 1:
                ret_code, final_state = result[0], result[1]
            else:
                ret_code, final_state = result, servo_status

            if final_state != 1:
                print(f"错误：未能成功进入就绪状态，当前状态为 {final_state} ({servo_names.get(final_state, '未知状态')})")
                return False

            print("步骤 3: 机器人已处于状态1 (就绪)。")

            # 第4步：根据文档要求，上电前需要先调用 set_servo_state(1)
            print("步骤 4: 上电前设置伺服状态...")
            result = nrc.set_servo_state(self.socket_fd, 1)
            if result != 0:
                print(f"警告：上电前设置伺服状态返回码: {result}")
            time.sleep(0.2)

            # 第5步：执行上电
            print("步骤 5: 执行上电操作...")
            result = nrc.set_servo_poweron(self.socket_fd)
            if result != 0:
                print(f"错误：上电失败！返回码: {result}")
                print("请检查：")
                print("  - 安全回路是否正常")
                print("  - 示教器是否设置为远程模式")
                print("  - 物理控制器是否正常")
                print("  - 急停按钮是否释放")
                return False

            # 第6步：最后确认是否进入运行状态
            time.sleep(1)  # 等待上电完成
            servo_status = 0
            result = nrc.get_servo_state(self.socket_fd, servo_status)
            if isinstance(result, list) and len(result) > 1:
                ret_code, final_state = result[0], result[1]
            else:
                ret_code, final_state = result, servo_status

            if final_state == 3:
                print(f"成功！机器人已上电，当前状态为 {final_state} ({servo_names.get(final_state, '未知状态')})。")
                print("✅ 机器人现在可以执行运动控制指令了！")
                return True
            else:
                print(f"错误：上电后状态异常，当前状态为 {final_state} ({servo_names.get(final_state, '未知状态')})。")
                return False

        except Exception as e:
            print(f"初始化和上电流程失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def test_robot_enable_sequence(self):
        """测试机器人使能序列（使用完整的初始化流程）"""
        print("\n=== 机器人使能序列测试 ===")
        print("注意：请确保在示教器上已设置伺服就绪并选择远程模式")

        try:
            # 1. 检查当前模式
            print("1. 检查机器人当前模式...")
            mode = 0
            result = nrc.get_current_mode(self.socket_fd, mode)
            if isinstance(result, list) and len(result) > 1:
                ret_code, mode_value = result[0], result[1]
            else:
                ret_code, mode_value = result, mode
            mode_names = {0: "示教模式", 1: "运行模式", 2: "远程模式"}
            mode_name = mode_names.get(mode_value, f"未知模式({mode_value})")
            print(f"当前模式: {mode_name} (返回码: {ret_code})")

            # 2. 检查伺服状态
            print("2. 检查伺服状态...")
            servo_status = 0
            result = nrc.get_servo_state(self.socket_fd, servo_status)
            if isinstance(result, list) and len(result) > 1:
                ret_code, servo_status_value = result[0], result[1]
            else:
                ret_code, servo_status_value = result, servo_status
            servo_names = {0: "停止", 1: "就绪", 2: "报警", 3: "运行"}
            servo_name = servo_names.get(servo_status_value, f"未知状态({servo_status_value})")
            print(f"当前伺服状态: {servo_name} (返回码: {ret_code})")

            # 3. 检查运行状态
            print("3. 检查运行状态...")
            running_status = 0
            result = nrc.get_robot_running_state(self.socket_fd, running_status)
            if isinstance(result, list) and len(result) > 1:
                ret_code, running_status_value = result[0], result[1]
            else:
                ret_code, running_status_value = result, running_status
            print(f"运行状态: {running_status_value} (返回码: {ret_code})")

            # 4. 执行完整的初始化和上电流程
            print("4. 机器人上电...")
            success = self.robot_initialize_and_power_on()

            if success:
                print("✅ 机器人初始化和上电成功！")
                return True
            else:
                print("❌ 机器人初始化和上电失败。请按照上面的建议检查设置。")
                return False

        except Exception as e:
            print(f"使能序列测试失败: {e}")
            return False


def test_robot_status_reading():
    """测试机器人状态读取功能"""
    robot = None
    try:
        print("=" * 60)
        print("机器人状态读取测试")
        print("=" * 60)

        # 连接机器人
        print(f"正在连接机器人 {ROBOT_IP}:{ROBOT_PORT}...")
        robot = RobotInterface(ROBOT_IP, ROBOT_PORT)

        # 创建状态读取器
        status_reader = RobotStatusReader(robot)

        # 读取各种状态信息
        status_reader.read_basic_status()

        # 测试机器人使能序列
        status_reader.test_robot_enable_sequence()

        # 继续读取其他状态
        status_reader.read_position_info()
        status_reader.read_robot_config()
        status_reader.read_motor_status()
        status_reader.read_sensor_data()
        status_reader.read_teachbox_status()
        status_reader.read_controller_info()
        status_reader.read_io_status()
        status_reader.read_dh_params()
        status_reader.read_joint_params()
        status_reader.read_single_cycle()

        print("\n" + "=" * 60)
        print("状态读取测试完成！")
        print("=" * 60)

    except RuntimeError as e:
        print(f"\n❌ 连接失败（这在没有实际硬件时是正常的）: {e}")
        print("测试框架工作正常，但需要实际的机器人硬件才能完成状态读取测试。")

    except Exception as e:
        print(f"\n❌ 发生意外错误: {e}")
        import traceback
        traceback.print_exc()

    finally:
        if robot:
            robot.disconnect()

if __name__ == "__main__":
    test_robot_status_reading()
